# ElevenLabs STT Backend

基于 FastAPI 的 ElevenLabs Speech-to-Text API 后端服务。

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd elevenlabs-stt-backend

# 复制环境配置
cp .env.example .env
# 编辑 .env 文件，配置你的参数
```

### 2. Docker 部署（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

### 3. 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动 Redis（需要单独安装）
redis-server

# 启动应用
python -m app.main
```

## 📋 API 文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 主要接口

#### 1. 上传音频文件
```
POST /api/v1/transcribe/upload
```

支持的参数：
- `file`: 音频/视频文件（必需）
- `language_code`: 语言代码（可选，如: zh, en, ja）
- `tag_audio_events`: 是否标记音频事件（默认: true）
- `diarize`: 是否启用说话人识别（默认: false）
- `num_speakers`: 最大说话人数量（可选，1-32）
- `timestamps_granularity`: 时间戳粒度（默认: word）
- `additional_formats`: 额外格式配置（可选，JSON字符串）

#### 2. 查询任务状态
```
GET /api/v1/transcribe/status/{task_id}
```

#### 3. 下载SRT文件
```
GET /api/v1/transcribe/download/{task_id}/{filename}
```

**参数说明**:
- `task_id`: 转录任务ID
- `filename`: SRT文件名（从任务状态响应中获取）

**使用示例**:
```bash
# 1. 先查询任务状态获取下载信息
curl "http://localhost:8000/api/v1/transcribe/status/your-task-id"

# 2. 使用返回的下载URL下载SRT文件
curl -O "http://localhost:8000/api/v1/transcribe/download/your-task-id/your-task-id.srt"
```

#### 4. 健康检查
```
GET /health
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `REDIS_URL` | Redis连接URL | `redis://localhost:6379/0` |
| `MAX_UPLOAD_SIZE` | 最大文件大小（字节） | `**********` (1GB) |
| `MAX_CONCURRENT_TASKS` | 最大并发任务数 | `3` |
| `LOG_LEVEL` | 日志级别 | `INFO` |

### 支持的文件格式

- 音频: MP3, WAV, FLAC, M4A, AAC, OGG
- 视频: MP4, MOV

### 支持的语言

zh, en, ja, ko, es, fr, de, ru, it, pt

### 说话人识别功能

说话人识别（Speaker Diarization）功能可以识别音频中的不同说话人，并在字幕中标记说话人信息。

#### 功能特点
- **可选控制**: 默认关闭，可通过 `diarize` 参数启用
- **多人对话**: 适用于访谈、会议、播客等多人场景
- **标记格式**: 在字幕中显示 `[speaker_0]`, `[speaker_1]` 等标记

#### 使用示例

**启用说话人识别**:
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -F "file=@audio.mp3" \
  -F "diarize=true" \
  -F "num_speakers=2"
```

**输出示例**:
```srt
1
00:00:00,119 --> 00:00:04,999
[speaker_0] When I woke up, sweating from the pain...

2
00:00:05,099 --> 00:00:09,939
[speaker_1] As a woman, you didn't even know...
```

**关闭说话人识别**（默认）:
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -F "file=@audio.mp3" \
  -F "diarize=false"
```

**输出示例**:
```srt
1
00:00:00,119 --> 00:00:04,999
When I woke up, sweating from the pain...

2
00:00:05,099 --> 00:00:09,939
As a woman, you didn't even know...
```

### 完整工作流程

#### 1. 上传音频文件
```bash
curl -X POST "http://localhost:8000/api/v1/transcribe/upload" \
  -F "file=@audio.mp3" \
  -F "diarize=true"
```

**响应示例**:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "pending",
  "message": "任务已创建，开始处理...",
  "file_info": {
    "filename": "audio.mp3",
    "size": 1024000,
    "duration": 120.5,
    "mime_type": "audio/mpeg"
  }
}
```

#### 2. 查询处理状态
```bash
curl "http://localhost:8000/api/v1/transcribe/status/abc123-def456-ghi789"
```

**响应示例**（处理完成）:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "completed",
  "progress": 100,
  "message": "转录完成",
  "srt_files": [
    {
      "filename": "abc123-def456-ghi789.srt",
      "download_url": "/api/v1/transcribe/download/abc123-def456-ghi789/abc123-def456-ghi789.srt",
      "content_type": "text/srt",
      "file_size": 2048,
      "duration": 120.5
    }
  ],
  "processing_time": 45.2
}
```

#### 3. 下载SRT文件
```bash
# 方法1: 直接下载
curl -O "http://localhost:8000/api/v1/transcribe/download/abc123-def456-ghi789/abc123-def456-ghi789.srt"

# 方法2: 指定输出文件名
curl "http://localhost:8000/api/v1/transcribe/download/abc123-def456-ghi789/abc123-def456-ghi789.srt" \
  -o "my_subtitles.srt"
```

## 🛠️ 开发指南

### 项目结构

```
app/
├── __init__.py
├── main.py                 # FastAPI 应用入口
├── models/
│   ├── request_models.py   # 请求模型定义
│   └── response_models.py  # 响应模型定义
├── services/
│   ├── elevenlabs_client.py # ElevenLabs API 客户端
│   ├── file_service.py     # 文件处理服务
│   └── task_service.py     # 任务管理服务
├── api/
│   └── transcribe.py       # 转录API路由
├── core/
│   ├── config.py           # 配置管理
│   └── dependencies.py     # 依赖注入
└── utils/
    ├── validators.py       # 参数验证
    └── helpers.py          # 工具函数
```

### 添加新功能

1. 在相应的模块中添加代码
2. 更新模型定义（如需要）
3. 添加测试
4. 更新文档

### 最近更新

#### v1.1.0 - 说话人识别可选控制
- ✅ 新增 `diarize` 参数，支持开关控制说话人识别
- ✅ 默认关闭说话人识别，避免不必要的标记
- ✅ 前端支持开关控制，提升用户体验
- ✅ 向后兼容，现有API调用不受影响

**迁移指南**:
- 如需保持原有行为（显示说话人标记），请设置 `diarize=true`
- 新用户默认获得更清洁的字幕输出（无说话人标记）

## 🚨 故障排除

### 常见问题

1. **Redis 连接失败**
   ```bash
   # 检查 Redis 是否运行
   docker-compose ps redis
   
   # 查看 Redis 日志
   docker-compose logs redis
   ```

2. **文件上传失败**
   - 检查文件大小是否超过限制
   - 验证文件格式是否支持
   - 确认磁盘空间充足

3. **转录任务超时**
   - 检查网络连接
   - 增加超时时间设置
   - 查看 ElevenLabs API 状态

4. **说话人识别问题**
   - 确认 `diarize` 参数设置正确
   - 单人音频建议关闭说话人识别（`diarize=false`）
   - 多人音频可设置 `num_speakers` 参数提高准确性
   - 检查音频质量，背景噪音可能影响识别效果

5. **SRT文件下载问题**
   - 确认任务状态为 `completed` 才能下载
   - 检查下载URL中的 `task_id` 和 `filename` 是否正确
   - 验证文件是否存在：查看任务状态响应中的 `srt_files` 字段
   - 下载链接有效期：文件会在一定时间后自动清理

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f api

# 查看特定时间的日志
docker-compose logs --since="2024-01-01T00:00:00" api
```

## 📊 监控

### 健康检查

```bash
curl http://localhost:8000/health
```

### 性能监控

应用包含以下监控指标：
- 请求响应时间
- 任务处理状态
- Redis 连接状态
- 文件上传统计

## 🔒 安全

- 文件类型验证
- 文件大小限制
- 请求频率限制
- CORS 配置
- 输入参数验证

## 📄 许可证

MIT License
